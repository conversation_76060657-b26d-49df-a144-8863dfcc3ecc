package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.data.DataInterface;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanBookListMapper;
import com.aaron.spring.mapper.custom.EnyanBookListCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanBookListService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/2/13
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanBookListServiceImpl implements EnyanBookListService {
	@Resource
	private EnyanBookListMapper enyanBookListMapper;

	@Resource
	private EnyanBookListCustomMapper enyanBookListCustomMapper;

	@Resource
	private DataInterface dataInterface;

	@Resource
	private EnyanBookService enyanBookService;

	@Override
	public Page queryRecords(Page<EnyanBookList> page, EnyanBookList record) {
		if (null == record){
			record = new EnyanBookList();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanBookListExample example = new EnyanBookListExample();
			EnyanBookListExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			//criteria.andIsDeletedEqualTo(0);
			if (StringUtils.isNotBlank(record.getSetName())){
				criteria.andSetNameLike("%"+record.getSetName()+"%");
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanBookListMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanBookList> list;
			if (count > 0){
				list = enyanBookListMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public Page queryBookRecords(Page<EnyanBook> page, EnyanBook record, EnyanBookList bookList) {
		if (null == record){
			record = new EnyanBook();
		}
		if (null == page){
			page = new Page<>();
		}
		try {

			String bookIdText = bookList.getBookIdText();
			if (StringUtils.isNotBlank(bookIdText) == false){
				bookIdText = "";
			}
			String[] allIds = bookIdText.split(",");
			
			// 过滤掉空字符串
			List<String> validIds = new ArrayList<>();
			for (String id : allIds) {
				if (StringUtils.isNotBlank(id)) {
					validIds.add(id.trim());
				}
			}
			String[] ids = validIds.toArray(new String[0]);

			long count = ids.length;

			List<EnyanBook> list = new ArrayList<>();
			page.setRecords(list);
			page.setTotalRecord(count);

			if (count > 0){
				int totalPages = page.getTotalPage();
				if (page.getCurrentPage() > totalPages){
					page.setCurrentPage(totalPages);
				}
				int pageRecordEnd = page.getRecordEnd();
				int recordEnd = pageRecordEnd > page.getTotalRecord() ? (int)count : pageRecordEnd;
				int recordStart = page.getRecordStart() - 1;
				for (int i = recordStart; i < recordEnd; i++) {
					Long bookId = Long.parseLong(ids[i]);
					EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
					list.add(enyanBook);
				}
			}


		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public List<EnyanBook> queryBookRecords(EnyanBookList bookList) {
		try {
			if (null == bookList){
				return null;
			}
			String bookIdText = bookList.getBookIdText();
			if (org.springframework.util.StringUtils.hasLength(bookIdText) == false){
				bookIdText = "";
			}
			String[] ids = bookIdText.split(",");

			List<EnyanBook> list = new ArrayList<>();
			for (int i = 0; i < ids.length; i++) {
				Long bookId = Long.parseLong(ids[i]);
				EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
				list.add(enyanBook);
			}
			return list;

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public void resetCache(Long dataId) {
		dataInterface.delBookListById(dataId+"");
	}

	@Override
	public List<EnyanBookList> findBookListByIndex(Integer index) {
		List<EnyanBookList> list = enyanBookListCustomMapper.findBookListByIndex(index);
		for (EnyanBookList obj : list){
			String bookIdText = obj.getBookIdText();
			if (StringUtils.isBlank(bookIdText) == true){
				bookIdText = "";
			}
			String[] ids = bookIdText.split(",");
			obj.setBookIDs(ids);
		}
		return list;
	}

	@Override
	public ExecuteResult<EnyanBookList> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanBookList> result = new ExecuteResult<>();
		try {
			EnyanBookList record = dataInterface.getBookListByID(pkId+"");
			if (null != record){
				result.setResult(record);
				return result;
			}
			record = enyanBookListMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanBookList();
			}
			if (StringUtils.isNotBlank(record.getBookWeb())){
				String bookWeb = record.getBookWeb();
				BookWebInfo bookWebInfo = JSON.parseObject(bookWeb,BookWebInfo.class);
				record.setBookWebInfo(bookWebInfo);
			}
			result.setResult(record);
			dataInterface.saveBookList(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBookList> addRecord(EnyanBookList record) {
		ExecuteResult<EnyanBookList> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanBookListMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBookList> updateRecord(EnyanBookList record) {
		ExecuteResult<EnyanBookList> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanBookListMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
			dataInterface.delBookListById(record.getSetId()+"");
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = enyanBookListCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanBookList record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanBookList record) {
		return null;
	}

	@Override
	public List<EnyanBookList> findByNames(List<String> names) {
		if (names == null || names.isEmpty()) {
			return new ArrayList<>();
		}
		return enyanBookListMapper.selectByNames(names);
	}

	@Override
	public List<Long> findAllBookListIds() {
		EnyanBookListExample example = new EnyanBookListExample();
		EnyanBookListExample.Criteria criteria = example.createCriteria();
		criteria.andIsValidEqualTo(1);
		List<EnyanBookList> bookLists = enyanBookListMapper.selectByExample(example);
		List<Long> setIds = new ArrayList<>();
		for (EnyanBookList bookList : bookLists) {
			if (bookList.getSetId() != null) {
				setIds.add(bookList.getSetId());
			}
		}
		return setIds;
	}


}
